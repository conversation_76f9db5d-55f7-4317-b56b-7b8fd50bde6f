import os
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from uuid import UUID

from app.database import get_db, OutputFile, Upload, User
from app.utils.auth import get_current_user

router = APIRouter(
    prefix="/download",
    tags=["download"],
    responses={404: {"description": "Not found"}},
)

@router.get("/{output_id}")
async def download_file(
    output_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Download a generated KML/KMZ file.
    """
    # Query the output file record and check if it belongs to the current user
    output_file = db.query(OutputFile).join(Upload).filter(
        OutputFile.id == output_id,
        Upload.user_id == current_user.id
    ).first()

    if not output_file:
        raise HTTPException(status_code=404, detail=f"Output file with ID {output_id} not found or does not belong to you")

    # Check if the file exists
    if not os.path.exists(output_file.file_path):
        raise HTTPException(status_code=404, detail="File not found on server")

    # Return the file
    return FileResponse(
        path=output_file.file_path,
        filename=output_file.filename,
        media_type="application/octet-stream"
    )
