import emails
import logging
import random
import string
from typing import Dict, Any
from emails.template import <PERSON><PERSON><PERSON><PERSON>plate
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='email.log',
    filemode='a'
)
logger = logging.getLogger(__name__)

from app.config import (
    EMAILS_FROM_EMAIL,
    EMAILS_FROM_NAME,
    SMTP_HOST,
    SMTP_PASSWORD,
    SMTP_PORT,
    SMTP_TLS,
    SMTP_USER,
    FRONTEND_URL,
)

def generate_verification_code(length: int = 6) -> str:
    """Generate a random verification code."""
    return ''.join(random.choices(string.digits, k=length))

def send_email(
    email_to: str,
    subject: str,
    html_template: str,
    environment: Dict[str, Any],
) -> bool:
    """
    Send an email using the configured SMTP server.

    Args:
        email_to: The recipient's email address
        subject: The email subject
        html_template: The HTML template for the email body
        environment: The template variables

    Returns:
        True if the email was sent successfully, False otherwise
    """
    try:
        logger.info(f"Attempting to send email to: {email_to}")
        logger.info(f"Using SMTP settings: Host={SMTP_HOST}, Port={SMTP_PORT}, User={SMTP_USER}")

        message = emails.Message(
            subject=subject,
            html=JinjaTemplate(html_template),
            mail_from=(EMAILS_FROM_NAME, EMAILS_FROM_EMAIL),
        )

        smtp_options = {
            "host": SMTP_HOST,
            "port": SMTP_PORT,
            "tls": SMTP_TLS,
        }

        if SMTP_USER and SMTP_PASSWORD:
            smtp_options["user"] = SMTP_USER
            smtp_options["password"] = SMTP_PASSWORD
            logger.info("Using SMTP authentication")
        else:
            logger.warning("WARNING: SMTP_USER or SMTP_PASSWORD not set")

        response = message.send(
            to=email_to,
            render=environment,
            smtp=smtp_options,
        )

        logger.info(f"Email send response: Status={response.status_code}, Error={getattr(response, 'error', 'None')}")
        return response.status_code == 250
    except Exception as e:
        logger.error(f"Error sending email: {str(e)}")
        return False

def send_verification_email(email_to: str, code: str) -> bool:
    """
    Send a verification email with a code.

    Args:
        email_to: The recipient's email address
        code: The verification code

    Returns:
        True if the email was sent successfully, False otherwise
    """
    subject = "Verify your email for KML Automation"
    html_template = """
    <html>
    <body>
        <h1>Welcome to KML Automation</h1>
        <p>Please use the following code to verify your email address:</p>
        <h2>{{ code }}</h2>
        <p>This code will expire in 15 minutes.</p>
        <p>If you didn't request this email, please ignore it.</p>
    </body>
    </html>
    """

    environment = {
        "code": code,
        "frontend_url": FRONTEND_URL,
    }

    return send_email(
        email_to=email_to,
        subject=subject,
        html_template=html_template,
        environment=environment,
    )
