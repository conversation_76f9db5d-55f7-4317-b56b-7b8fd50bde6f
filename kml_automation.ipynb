{"cells": [{"cell_type": "code", "execution_count": 10, "id": "4791717c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 10270 entries, 0 to 10269\n", "Data columns (total 40 columns):\n", " #   Column                      Non-Null Count  Dtype  \n", "---  ------                      --------------  -----  \n", " 0   Server                      10256 non-null  object \n", " 1   Seq                         10270 non-null  int64  \n", " 2   Ok                          10270 non-null  object \n", " 3   Band                        10270 non-null  int64  \n", " 4   Cell ID                     10270 non-null  object \n", " 5   Cell ID 2                   10270 non-null  object \n", " 6   eNodeB                      10270 non-null  int64  \n", " 7   Category                    10270 non-null  object \n", " 8   Cell Name                   10270 non-null  object \n", " 9   Key 1                       10270 non-null  object \n", " 10  Cell Key 1                  10270 non-null  object \n", " 11  Cell Key                    10270 non-null  object \n", " 12  Key3                        72 non-null     object \n", " 13  cell                        10270 non-null  object \n", " 14  eNodeB Name                 9678 non-null   object \n", " 15  Longitude                   10270 non-null  float64\n", " 16  Latitude                    10270 non-null  float64\n", " 17  On-Air Date                 10270 non-null  object \n", " 18  Technology                  10270 non-null  object \n", " 19  Towe Type                   10270 non-null  object \n", " 20  FOPs POC                    10270 non-null  object \n", " 21  Vendor                      10270 non-null  object \n", " 22  Address                     10270 non-null  object \n", " 23  Grid                        10270 non-null  object \n", " 24  Azimuth                     10270 non-null  int64  \n", " 25  Mtilt                       10270 non-null  int64  \n", " 26  Etilt                       10268 non-null  object \n", " 27  PCI                         10270 non-null  object \n", " 28  TAC                         10270 non-null  object \n", " 29  AH                          10074 non-null  object \n", " 30  Bandwidth                   10270 non-null  int64  \n", " 31  Radius                      10270 non-null  float64\n", " 32  4G Traffic                  10252 non-null  object \n", " 33  DL PRB Rate                 10270 non-null  float64\n", " 34  Unnamed: 34                 0 non-null      float64\n", " 35  Cell Availability           10270 non-null  float64\n", " 36  Average User DL Throughput  10270 non-null  float64\n", " 37  Volte Traffic (erl)         10270 non-null  float64\n", " 38  Rs Port No                  10256 non-null  object \n", " 39  Ant bit map                 10256 non-null  object \n", "dtypes: float64(8), int64(6), object(26)\n", "memory usage: 3.1+ MB\n", "None\n", "KMZ file generated: cell_analysis_dl_prb.kmz\n"]}], "source": ["import math\n", "import simplekml\n", "import pandas as pd\n", "\n", "\n", "def generate_pie_wedge(kml, lat, lon, azimuth, radius, cell_name, color, cell_data):\n", "    num_points = 36\n", "    start_angle = azimuth - 20\n", "    end_angle = azimuth + 20\n", "    step_angle = (end_angle - start_angle) / num_points\n", "    coords = [(lon, lat)]\n", "\n", "    for i in range(num_points + 1):\n", "        angle = math.radians(start_angle + i * step_angle)\n", "        lat_offset = lat + (radius * math.cos(angle)) / 111.32\n", "        lon_offset = lon + (radius * math.sin(angle)) / (111.32 * math.cos(math.radians(lat)))\n", "        coords.append((lon_offset, lat_offset))\n", "\n", "    polygon = kml.newpolygon(name=cell_name, outerboundaryis=coords)\n", "    polygon.style.polystyle.color = color\n", "    polygon.style.polystyle.outline = 1\n", "\n", "    description = f\"\"\"\n", "    <table border=\"1\">\n", "        <tr><th>Field</th><th>Value</th></tr>\n", "        {\"\".join([f\"<tr><td>{key}</td><td>{value}</td></tr>\" for key, value in cell_data.items()])}\n", "    </table>\n", "    \"\"\"\n", "    polygon.description = description\n", "\n", "\n", "# Load CSV data\n", "csv_file = \"kmldata.csv\"  # Update with your CSV file path\n", "data = pd.read_csv(csv_file)\n", "print(data.info())\n", "# Radius mapping based on Band\n", "radius_mapping = {900: 0.2, 1800: 0.17, 2100: 0.15}\n", "\n", "# Function to get color based on DL PRB\n", "def get_color(dl_prb):\n", "    if dl_prb >= 0.85:\n", "        return \"7f0000ff\"  # Red (Alpha=7F, Blue=00, Green=00, Red=FF)\n", "    elif 0.70 <= dl_prb < 0.85:\n", "        return \"7f00ffff\"  # Yellow (Alpha=7F, Blue=00, Green=FF, Red=FF)\n", "    else:\n", "        return \"7f00ff00\"  # Green (Alpha=7F, Blue=00, Green=FF, Red=00)\n", "\n", "\n", "# Initialize KML\n", "kml = simplekml.Kml()\n", "\n", "for _, row in data.iterrows():\n", "    try:\n", "        band = row[\"Band\"]\n", "        dl_prb = row[\"DL PRB Rate\"]  # Replace with your actual column name for DL PRB\n", "        color = get_color(dl_prb)\n", "        radius = radius_mapping.get(band, 0.15)  # Default radius for unknown bands\n", "        cell_data = {key: row[key] if pd.notnull(row[key]) else \"N/A\" for key in row.index}\n", "        \n", "        generate_pie_wedge(\n", "            kml,\n", "            lat=row[\"Latitude\"],\n", "            lon=row[\"Longitude\"],\n", "            azimuth=row[\"Azimuth\"],\n", "            radius=radius,\n", "            cell_name=row[\"Cell Name\"],\n", "            color=color,\n", "            cell_data=cell_data,\n", "        )\n", "    except Exception as e:\n", "        print(f\"Error processing row: {row}. Error: {e}\")\n", "\n", "# Save KMZ\n", "kml.savekmz(\"cell_analysis_dl_prb.kmz\")\n", "print(\"KMZ file generated: cell_analysis_dl_prb.kmz\")"]}, {"cell_type": "code", "execution_count": 12, "id": "1650e140", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded data:\n", "  Server  Seq        Ok  Band Cell ID Cell ID 2  eNodeB  Category Cell Name   \n", "0    UME    3     L1800  1800       1         A    5130  Platinum    5130_1  \\\n", "1    UME    2  MM L2100  2100     2M4       2M4    4682    Silver  4682_2M4   \n", "2    UME    1  MM L1800  1800     164       3M3    6062    Silver  6062_164   \n", "3    UME    2  MM L1800  1800      64       3M3    6062    Silver   6062_64   \n", "4    UME    1  MM L1800  1800     165       3M4    6062    Silver  6062_165   \n", "\n", "       Key 1  ... <PERSON>width Radius   4G Traffic DL PRB Rate Unnamed: 34   \n", "0    5130L_1  ...        20   0.11  15.60827143    0.023412         NaN  \\\n", "1  4682L_2M4  ...         8   0.11  0.961085714    0.031080         NaN   \n", "2  6062L_164  ...         9   0.12  2.314071429    0.033457         NaN   \n", "3   6062L_64  ...         8   0.11  2.314071429    0.033457         NaN   \n", "4  6062L_165  ...         9   0.12  2.301828571    0.033604         NaN   \n", "\n", "   Cell Availability  Average User DL Throughput Volte Traffic (erl)   \n", "0              0.109                    2.080000                 0.0  \\\n", "1              1.000                   11.764429                 0.0   \n", "2              1.000                   22.878857                 0.0   \n", "3              1.000                   22.878857                 0.0   \n", "4              1.000                   17.792286                 6.0   \n", "\n", "  Rs Port No                                        Ant bit map  \n", "0       4[2]  1;0;0;1;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;...  \n", "1       4[2]  1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;...  \n", "2       4[2]  1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;...  \n", "3       4[2]  1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;...  \n", "4       4[2]  1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;1;...  \n", "\n", "[5 rows x 40 columns]\n", "KML file generated: site_points_unique.kml\n"]}], "source": ["import simplekml\n", "import pandas as pd\n", "\n", "# Function to get the appropriate icon based on category\n", "def get_icon_url(category):\n", "    icon_mapping = {\n", "        \"Platinum\": \"http://maps.google.com/mapfiles/kml/paddle/blu-diamond.png\",\n", "        \"Gold\": \"http://maps.google.com/mapfiles/kml/paddle/ylw-diamond.png\",\n", "        \"Silver\": \"http://maps.google.com/mapfiles/kml/paddle/wht-diamond.png\",\n", "        \"Bronze\": \"http://maps.google.com/mapfiles/kml/paddle/red-diamond.png\",\n", "        \"Strategic\": \"http://maps.google.com/mapfiles/kml/paddle/ltblu-diamond.png\",\n", "    }\n", "    return icon_mapping.get(category, \"http://maps.google.com/mapfiles/kml/paddle/E.png\")  # Default to \"E\" if category is missing\n", "\n", "# Load CSV data\n", "csv_file = \"kmldata.csv\"  # Replace with your CSV file path\n", "try:\n", "    data = pd.read_csv(csv_file)\n", "    print(f\"Loaded data:\\n{data.head()}\")  # Print first few rows for debugging\n", "except Exception as e:\n", "    print(f\"Error loading CSV file: {e}\")\n", "    exit()\n", "\n", "# Filter out duplicates based on Longitude and Latitude to get unique sites\n", "unique_sites = data.drop_duplicates(subset=[\"Longitude\", \"Latitude\"])\n", "\n", "# Initialize KML\n", "kml = simplekml.Kml()\n", "\n", "# Process each unique site\n", "for _, row in unique_sites.iterrows():\n", "    try:\n", "        # Extract values\n", "        category = row[\"Category\"]\n", "        enodeb = row[\"eNodeB\"]\n", "        enodeb_name = row[\"eNodeB Name\"]\n", "        lon = row[\"Longitude\"]\n", "        lat = row[\"Latitude\"]\n", "        on_air_date = row[\"On-Air Date\"]\n", "        technology = row[\"Technology\"]\n", "        tower_type = row[\"Towe Type\"]\n", "        fops_poc = row[\"FOPs POC\"]\n", "        vendor = row[\"Vendor\"]\n", "        address = row[\"Address\"]\n", "        grid = row[\"Grid\"]\n", "        tac = row[\"TAC\"]\n", "        bandwidth = row[\"Bandwidth\"]\n", "        radius = row[\"Radius\"]\n", "\n", "        # Ensure values are not missing\n", "        if pd.isnull(category) or pd.isnull(lon) or pd.isnull(lat):\n", "            continue\n", "\n", "        # Get the icon URL based on the category\n", "        icon_url = get_icon_url(category)\n", "\n", "        # Create a point with the category-specific marker\n", "        point = kml.newpoint(\n", "            name=f\"{enodeb}\",\n", "            coords=[(lon, lat)]\n", "        )\n", "        point.style.iconstyle.icon.href = icon_url  # Set icon based on category\n", "        point.style.iconstyle.scale = 1  # Normal size marker\n", "\n", "        # Description for popup info in Google Earth\n", "        \n", "        description = f\"\"\"\n", "        <table border=\"1\">\n", "            <tr><th>Field</th><th>Value</th></tr>\n", "            {\"\".join([f\"<tr><td>{key}</td><td>{value}</td></tr>\" for key, value in cell_data.items()])}\n", "        </table>\n", "        \"\"\"\n", "        point.description = description        \n", "        \n", "#         description = f\"\"\"\n", "#         <table border=\"1\">\n", "#             <tr><th>Field</th><th>Value</th></tr>\n", "#             <tr><td>eNodeB</td><td>{enodeb}</td></tr>\n", "#             <tr><td>Category</td><td>{category}</td></tr>\n", "#             <tr><td>eNodeB Name</td><td>{enodeb_name}</td></tr>\n", "#             <tr><td>Longitude</td><td>{lon}</td></tr>\n", "#             <tr><td>Latitude</td><td>{lat}</td></tr>\n", "#             <tr><td>On-Air Date</td><td>{on_air_date}</td></tr>\n", "#             <tr><td>Technology</td><td>{technology}</td></tr>\n", "#             <tr><td>Tower Type</td><td>{tower_type}</td></tr>\n", "#             <tr><td>FOPs POC</td><td>{fops_poc}</td></tr>\n", "#             <tr><td>Vendor</td><td>{vendor}</td></tr>\n", "#             <tr><td>Address</td><td>{address}</td></tr>\n", "#             <tr><td>Grid</td><td>{grid}</td></tr>\n", "#             <tr><td>TAC</td><td>{tac}</td></tr>\n", "#             <tr><td>Bandwidth</td><td>{bandwidth}</td></tr>\n", "#             <tr><td>Radius</td><td>{radius}</td></tr>\n", "#         </table>\n", "#         \"\"\"\n", "#         point.description = description\n", "\n", "    except Exception as e:\n", "        print(f\"Error processing row: {row.to_dict()}. Error: {e}\")\n", "\n", "# Save KML\n", "try:\n", "    kml.save(\"site_points_unique.kml\")\n", "    print(\"KML file generated: site_points_unique.kml\")\n", "except Exception as e:\n", "    print(f\"Error saving KML file: {e}\")\n"]}, {"cell_type": "code", "execution_count": 1, "id": "dd8839c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded data:\n", "  Server  Seq     Ok  Band Cell ID Cell ID 2  eNodeB Category Cell Name   \n", "0    EMS    3  L1800  1800       1         A    3008     Gold    3008_1  \\\n", "1    EMS    4  L2100  2100     101         A    3008     Gold  3008_101   \n", "2    EMS    4  L2100  2100     102         B    3008     Gold  3008_102   \n", "3    EMS    4  L2100  2100     103         C    3008     Gold  3008_103   \n", "4    EMS    3  L1800  1800       2         B    3008     Gold    3008_2   \n", "\n", "       Key 1  ...  AH Bandwidth Radius   4G Traffic DL PRB Rate   \n", "0    3008L_1  ...  41        20   0.11  269.9094714    0.814500  \\\n", "1  3008L_101  ...  38        16   0.10  76.45137143    0.513614   \n", "2  3008L_102  ...  38        16   0.10  51.36941429    0.374843   \n", "3  3008L_103  ...  38        16   0.10  59.42312857    0.626757   \n", "4    3008L_2  ...  35        20   0.11  112.6204857    0.345814   \n", "\n", "   Cell Availability  Average User DL Throughput Volte Traffic (erl)   \n", "0                1.0                    1.615414             49.8593  \\\n", "1                1.0                    2.916914             14.6133   \n", "2                1.0                    4.948157              8.4727   \n", "3                1.0                    2.226786              8.1114   \n", "4                1.0                    4.322986             14.9589   \n", "\n", "  Rs Port No                                        Ant bit map  \n", "0          1  1;0;0;1;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;...  \n", "1          0  1;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;...  \n", "2          0  1;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;...  \n", "3          0  1;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;...  \n", "4          1  1;0;0;1;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;...  \n", "\n", "[5 rows x 39 columns]\n", "KML file generated: site_points_unique.kml\n"]}], "source": ["# import simplekml\n", "# import pandas as pd\n", "\n", "# # Load CSV data\n", "# csv_file = \"kmldata.csv\"  # Replace with your CSV file path\n", "# try:\n", "#     data = pd.read_csv(csv_file)\n", "#     print(f\"Loaded data:\\n{data.head()}\")  # Print first few rows for debugging\n", "# except Exception as e:\n", "#     print(f\"Error loading CSV file: {e}\")\n", "#     exit()\n", "\n", "# # Function to get point color based on Category\n", "# def get_point_color(category):\n", "#     color_mapping = {\n", "#         \"Platinum\": \"ff0000ff\",  # Blue\n", "#         \"Gold\": \"7fffffff\",      # Yellow\n", "#         \"Silver\": \"7fffff00\",    # White\n", "#         \"Bronze\": \"7fff0000\",    # Red\n", "#         \"Strategic\": \"7f800080\", # Purple (default for Strategic or other)\n", "#     }\n", "#     return color_mapping.get(category, \"7f800080\")  # Default to Purple if unknown\n", "\n", "# # Filter out duplicates based on Latitude and Longitude to get unique sites\n", "# unique_sites = data.drop_duplicates(subset=[\"Longitude\", \"Latitude\"])\n", "\n", "# # Initialize KML\n", "# kml = simplekml.Kml()\n", "\n", "# # Process each unique site\n", "# for _, row in unique_sites.iterrows():\n", "#     try:\n", "#         # Extract values\n", "#         category = row[\"Category\"]\n", "#         enodeb = row[\"eNodeB\"]\n", "#         enodeb_name = row[\"eNodeB Name\"]\n", "#         lon = row[\"Longitude\"]\n", "#         lat = row[\"Latitude\"]\n", "#         on_air_date = row[\"On-Air Date\"]\n", "#         technology = row[\"Technology\"]\n", "#         tower_type = row[\"Towe Type\"]\n", "#         fops_poc = row[\"FOPs POC\"]\n", "#         vendor = row[\"Vendor\"]\n", "#         address = row[\"Address\"]\n", "#         grid = row[\"Grid\"]\n", "#         tac = row[\"TAC\"]\n", "#         bandwidth = row[\"Bandwidth\"]\n", "#         radius = row[\"Radius\"]\n", "\n", "#         # Ensure values are not missing\n", "#         if pd.isnull(category) or pd.isnull(lon) or pd.isnull(lat):\n", "#             continue\n", "\n", "#         color = get_point_color(category)\n", "\n", "#         # Create a point with a normal size location marker\n", "#         point = kml.newpoint(\n", "#             name=f\"{enodeb}\",\n", "#             coords=[(lon, lat)]\n", "#         )\n", "#         point.style.iconstyle.color = color\n", "#         point.style.iconstyle.scale = 1  # Normal size, 1 is the default size for the marker\n", "\n", "#         description = f\"\"\"\n", "#         <table border=\"1\">\n", "#             <tr><th>Field</th><th>Value</th></tr>\n", "#             <tr><td>eNodeB</td><td>{enodeb}</td></tr>\n", "#             <tr><td>Category</td><td>{category}</td></tr>\n", "#             <tr><td>eNodeB Name</td><td>{enodeb_name}</td></tr>\n", "#             <tr><td>Longitude</td><td>{lon}</td></tr>\n", "#             <tr><td>Latitude</td><td>{lat}</td></tr>\n", "#             <tr><td>On-Air Date</td><td>{on_air_date}</td></tr>\n", "#             <tr><td>Technology</td><td>{technology}</td></tr>\n", "#             <tr><td>Tower Type</td><td>{tower_type}</td></tr>\n", "#             <tr><td>FOPs POC</td><td>{fops_poc}</td></tr>\n", "#             <tr><td>Vendor</td><td>{vendor}</td></tr>\n", "#             <tr><td>Address</td><td>{address}</td></tr>\n", "#             <tr><td>Grid</td><td>{grid}</td></tr>\n", "#             <tr><td>TAC</td><td>{tac}</td></tr>\n", "#             <tr><td>Bandwidth</td><td>{bandwidth}</td></tr>\n", "#             <tr><td>Radius</td><td>{radius}</td></tr>\n", "#         </table>\n", "#         \"\"\"\n", "#         point.description = description\n", "\n", "#     except Exception as e:\n", "#         print(f\"Error processing row: {row.to_dict()}. Error: {e}\")\n", "\n", "# # Save KML\n", "# try:\n", "#     kml.save(\"site_points_unique.kml\")\n", "#     print(\"KML file generated: site_points_unique.kml\")\n", "# except Exception as e:\n", "#     print(f\"Error saving KML file: {e}\")\n"]}, {"cell_type": "code", "execution_count": 5, "id": "bd07f370", "metadata": {}, "outputs": [], "source": ["# import folium\n", "\n", "# # Example data\n", "# data = [\n", "#     {\"lat\": 25, \"lon\": 45, \"value\": 10},\n", "#     {\"lat\": 22, \"lon\": 50, \"value\": 20},\n", "#     {\"lat\": 18, \"lon\": 55, \"value\": 15},\n", "# ]\n", "\n", "# # Create the map\n", "# map_ = folium.Map(location=[22, 50], zoom_start=5)\n", "\n", "# # Add bar-like markers\n", "# for row in data:\n", "#     folium.CircleMarker(\n", "#         location=(row[\"lat\"], row[\"lon\"]),\n", "#         radius=row[\"value\"],  # Radius proportional to value\n", "#         color=\"blue\",\n", "#         fill=True,\n", "#         fill_color=\"blue\",\n", "#         fill_opacity=0.7,\n", "#     ).add_to(map_)\n", "\n", "# map_.save(\"map_with_bars.html\")"]}, {"cell_type": "code", "execution_count": 6, "id": "96a457ff", "metadata": {}, "outputs": [], "source": ["# import matplotlib.pyplot as plt\n", "# import simplekml\n", "# import os\n", "\n", "# # Sample data\n", "# data = [\n", "#     {\"cell_id\": \"Cell1\", \"lat\": 24.7136, \"lon\": 46.6753, \"rs_power\": 16, \"pa_power\": 50, \"pb_power\": 50},\n", "#     {\"cell_id\": \"Cell2\", \"lat\": 24.7146, \"lon\": 46.6763, \"rs_power\": 20, \"pa_power\": 45, \"pb_power\": 55},\n", "# ]\n", "\n", "# # Output folder for charts\n", "# output_folder = \"charts\"\n", "# os.makedirs(output_folder, exist_ok=True)\n", "\n", "# # Generate bar charts and save as images\n", "# for cell in data:\n", "#     plt.figure(figsize=(4, 3))\n", "#     plt.bar([\"RS Power\", \"PA Power\", \"PB Power\"], [cell[\"rs_power\"], cell[\"pa_power\"], cell[\"pb_power\"]], color=[\"red\", \"blue\", \"green\"])\n", "#     plt.title(f\"Power Distribution: {cell['cell_id']}\")\n", "#     plt.ylabel(\"Power (dBm)\")\n", "#     plt.savefig(f\"{output_folder}/{cell['cell_id']}.png\")\n", "#     plt.close()\n", "\n", "# # Create KML\n", "# kml = simplekml.Kml()\n", "\n", "# for cell in data:\n", "#     # Add Placemark\n", "#     pnt = kml.newpoint(name=cell[\"cell_id\"], coords=[(cell[\"lon\"], cell[\"lat\"])])\n", "#     pnt.description = f\"RS Power: {cell['rs_power']} dBm\\nPA Power: {cell['pa_power']} dBm\\nPB Power: {cell['pb_power']} dBm\"\n", "\n", "#     # Add Overlay (chart)\n", "#     ground = kml.newgroundoverlay(name=f\"{cell['cell_id']} Chart\")\n", "#     ground.icon.href = f\"./{output_folder}/{cell['cell_id']}.png\"\n", "#     ground.latlonbox.north = cell[\"lat\"] + 0.001\n", "#     ground.latlonbox.south = cell[\"lat\"] - 0.001\n", "#     ground.latlonbox.east = cell[\"lon\"] + 0.001\n", "#     ground.latlonbox.west = cell[\"lon\"] - 0.001\n", "\n", "# # Save KML\n", "# kml.save(\"Power_Distribution.kml\")"]}, {"cell_type": "code", "execution_count": null, "id": "705b297a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.1"}}, "nbformat": 4, "nbformat_minor": 5}