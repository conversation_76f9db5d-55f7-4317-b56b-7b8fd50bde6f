2025-05-20 16:28:00,423 - app.routers.auth - INFO - Signup request received for email: <EMAIL>
2025-05-20 16:28:01,287 - app.utils.email - INFO - Attempting to send email to: <EMAIL>
2025-05-20 16:28:01,288 - app.utils.email - INFO - Using SMTP settings: Host=smtp.gmail.com, Port=587, User=<EMAIL>
2025-05-20 16:28:01,450 - app.utils.email - INFO - Using SMTP authentication
2025-05-20 16:28:06,543 - app.utils.email - INFO - Email send response: Status=None, Error=[Errno None] (535, b'5.7.8 Username and Password not accepted. For more information, go to\n5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-448ba3d8facsm2026525e9.6 - gsmtp'): None
2025-05-20 16:29:38,519 - app.routers.auth - INFO - Signup request received for email: <EMAIL>
2025-05-20 16:29:40,222 - app.utils.email - INFO - Attempting to send email to: <EMAIL>
2025-05-20 16:29:40,222 - app.utils.email - INFO - Using SMTP settings: Host=smtp.gmail.com, Port=587, User=<EMAIL>
2025-05-20 16:29:40,222 - app.utils.email - INFO - Using SMTP authentication
2025-05-20 16:29:42,538 - app.utils.email - INFO - Email send response: Status=None, Error=[Errno None] (535, b'5.7.8 Username and Password not accepted. For more information, go to\n5.7.8  https://support.google.com/mail/?p=BadCredentials ffacd0b85a97d-3a35ca4d1f9sm15874689f8f.1 - gsmtp'): None
2025-05-20 16:33:48,052 - app.routers.auth - INFO - Signup request received for email: <EMAIL>
2025-05-20 16:38:21,122 - app.routers.auth - INFO - Signup request received for email: <EMAIL>
2025-05-20 16:38:21,829 - app.utils.email - INFO - Attempting to send email to: <EMAIL>
2025-05-20 16:38:21,829 - app.utils.email - INFO - Using SMTP settings: Host=smtp.gmail.com, Port=587, User=<EMAIL>
2025-05-20 16:38:22,024 - app.utils.email - INFO - Using SMTP authentication
2025-05-20 16:38:25,484 - app.utils.email - INFO - Email send response: Status=250, Error=None
2025-05-20 16:41:17,177 - app.utils.email - INFO - Attempting to send email to: <EMAIL>
2025-05-20 16:41:17,178 - app.utils.email - INFO - Using SMTP settings: Host=smtp.gmail.com, Port=587, User=<EMAIL>
2025-05-20 16:41:17,178 - app.utils.email - INFO - Using SMTP authentication
2025-05-20 16:41:20,043 - app.utils.email - INFO - Email send response: Status=250, Error=None
2025-05-20 16:53:57,784 - app.routers.auth - INFO - Signup request received for email: <EMAIL>
2025-05-20 17:40:10,285 - app.routers.auth - INFO - Signup request received for email: <EMAIL>
2025-05-20 17:40:28,999 - app.routers.auth - INFO - Signup request received for email: <EMAIL>
2025-05-20 17:41:00,102 - app.routers.auth - INFO - Signup request received for email: <EMAIL>
2025-05-20 17:41:23,143 - app.routers.auth - INFO - Signup request received for email: <EMAIL>
2025-05-20 17:48:29,392 - app.routers.auth - INFO - Signup request received for email: <EMAIL>
2025-05-20 17:48:49,393 - app.routers.auth - INFO - Signup request received for email: <EMAIL>
2025-05-20 18:06:53,576 - app.routers.auth - INFO - Verification code request received for email: <EMAIL>
2025-05-20 18:07:10,489 - app.routers.auth - INFO - Verification code request received for email: <EMAIL>
2025-05-20 18:07:12,856 - app.utils.email - INFO - Attempting to send email to: <EMAIL>
2025-05-20 18:07:12,856 - app.utils.email - INFO - Using SMTP settings: Host=smtp.gmail.com, Port=587, User=<EMAIL>
2025-05-20 18:07:13,118 - app.utils.email - INFO - Using SMTP authentication
2025-05-20 18:07:16,598 - app.utils.email - INFO - Email send response: Status=250, Error=None
2025-05-20 18:07:16,598 - app.routers.auth - INFO - Verification code sent to new user: <EMAIL>
2025-05-20 18:08:30,114 - app.routers.auth - INFO - Verification request received for email: <EMAIL>, code: 704148
2025-05-20 18:08:30,426 - app.routers.auth - INFO - User found: <EMAIL>, is_active: False, verification_code: 704148
2025-05-20 18:08:58,745 - app.routers.auth - INFO - Set password request received for email: <EMAIL>
2025-05-20 18:08:58,969 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\kml-automation-NfjAMIAS-py3.11\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-20 18:08:58,977 - passlib.handlers.bcrypt - DEBUG - detected 'bcrypt' backend, version '<unknown>'
2025-05-20 18:08:58,978 - passlib.handlers.bcrypt - DEBUG - 'bcrypt' backend lacks $2$ support, enabling workaround
2025-05-20 18:08:59,838 - app.routers.auth - INFO - Password set successfully for user: <EMAIL>
2025-05-20 18:09:16,734 - app.routers.auth - INFO - Login request received for email: <EMAIL>
2025-05-20 18:09:17,785 - app.routers.auth - INFO - Login successful for user: <EMAIL>
2025-05-20 18:18:14,015 - app.routers.auth - INFO - Login request received for email: <EMAIL>
2025-05-20 18:18:19,162 - app.routers.auth - INFO - Login request received for email: <EMAIL>
2025-05-20 18:18:21,772 - app.routers.auth - INFO - Login successful for user: <EMAIL>
2025-05-20 18:29:59,327 - app.routers.auth - INFO - Login request received for email: <EMAIL>
2025-05-20 18:30:01,521 - app.routers.auth - INFO - Login request received for email: <EMAIL>
2025-05-20 18:30:04,119 - app.routers.auth - INFO - Login successful for user: <EMAIL>
2025-05-20 22:26:49,842 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-20 22:26:49,843 - multipart.multipart - DEBUG - Calling on_field_name with data[0:10]
2025-05-20 22:26:49,843 - multipart.multipart - DEBUG - Calling on_field_data with data[11:19]
2025-05-20 22:26:49,844 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-20 22:26:49,844 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-20 22:26:49,844 - multipart.multipart - DEBUG - Calling on_field_name with data[20:28]
2025-05-20 22:26:49,844 - multipart.multipart - DEBUG - Calling on_field_data with data[29:62]
2025-05-20 22:26:49,844 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-20 22:26:49,844 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-20 22:26:49,844 - multipart.multipart - DEBUG - Calling on_field_name with data[63:71]
2025-05-20 22:26:49,844 - multipart.multipart - DEBUG - Calling on_field_data with data[72:76]
2025-05-20 22:26:49,845 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-20 22:26:49,845 - multipart.multipart - DEBUG - Calling on_end with no data
2025-05-20 22:26:49,876 - app.routers.auth - INFO - Login request received for email: <EMAIL>
2025-05-20 22:26:50,089 - app.routers.auth - ERROR - User not found: <EMAIL>
2025-05-20 22:26:51,628 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-20 22:26:51,628 - multipart.multipart - DEBUG - Calling on_field_name with data[0:10]
2025-05-20 22:26:51,628 - multipart.multipart - DEBUG - Calling on_field_data with data[11:19]
2025-05-20 22:26:51,629 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-20 22:26:51,629 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-20 22:26:51,629 - multipart.multipart - DEBUG - Calling on_field_name with data[20:28]
2025-05-20 22:26:51,629 - multipart.multipart - DEBUG - Calling on_field_data with data[29:62]
2025-05-20 22:26:51,629 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-20 22:26:51,629 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-20 22:26:51,629 - multipart.multipart - DEBUG - Calling on_field_name with data[63:71]
2025-05-20 22:26:51,629 - multipart.multipart - DEBUG - Calling on_field_data with data[72:76]
2025-05-20 22:26:51,630 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-20 22:26:51,630 - multipart.multipart - DEBUG - Calling on_end with no data
2025-05-20 22:26:51,633 - app.routers.auth - INFO - Login request received for email: <EMAIL>
2025-05-20 22:26:51,847 - app.routers.auth - ERROR - User not found: <EMAIL>
2025-05-20 22:30:18,963 - app.routers.auth - INFO - Verification code request received for email: <EMAIL>
2025-05-20 22:30:19,938 - app.utils.email - INFO - Attempting to send email to: <EMAIL>
2025-05-20 22:30:19,938 - app.utils.email - INFO - Using SMTP settings: Host=smtp.gmail.com, Port=587, User=<EMAIL>
2025-05-20 22:30:20,337 - app.utils.email - INFO - Using SMTP authentication
2025-05-20 22:30:23,431 - app.utils.email - INFO - Email send response: Status=250, Error=None
2025-05-20 22:30:23,431 - app.routers.auth - INFO - Verification code sent to new user: <EMAIL>
2025-05-20 22:31:26,470 - app.routers.auth - INFO - Verification code request received for email: <EMAIL>
2025-05-20 22:31:27,144 - app.utils.email - INFO - Attempting to send email to: <EMAIL>
2025-05-20 22:31:27,145 - app.utils.email - INFO - Using SMTP settings: Host=smtp.gmail.com, Port=587, User=<EMAIL>
2025-05-20 22:31:27,145 - app.utils.email - INFO - Using SMTP authentication
2025-05-20 22:31:29,739 - app.utils.email - INFO - Email send response: Status=250, Error=None
2025-05-20 22:31:29,739 - app.routers.auth - INFO - Verification code sent to new user: <EMAIL>
2025-05-20 22:31:58,990 - app.routers.auth - INFO - Verification request received for email: <EMAIL>, code: 199296
2025-05-20 22:31:59,202 - app.routers.auth - INFO - User found: <EMAIL>, is_active: False, verification_code: 199296
2025-05-20 22:32:18,098 - app.routers.auth - INFO - Set password request received for email: <EMAIL>
2025-05-20 22:32:18,312 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\kml-automation-NfjAMIAS-py3.11\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-20 22:32:18,325 - passlib.handlers.bcrypt - DEBUG - detected 'bcrypt' backend, version '<unknown>'
2025-05-20 22:32:18,326 - passlib.handlers.bcrypt - DEBUG - 'bcrypt' backend lacks $2$ support, enabling workaround
2025-05-20 22:32:19,465 - app.routers.auth - INFO - Password set successfully for user: <EMAIL>
2025-05-20 22:32:31,777 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-20 22:32:31,777 - multipart.multipart - DEBUG - Calling on_field_name with data[0:10]
2025-05-20 22:32:31,777 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-20 22:32:31,777 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-20 22:32:31,778 - multipart.multipart - DEBUG - Calling on_field_name with data[12:20]
2025-05-20 22:32:31,778 - multipart.multipart - DEBUG - Calling on_field_data with data[21:54]
2025-05-20 22:32:31,778 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-20 22:32:31,778 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-20 22:32:31,778 - multipart.multipart - DEBUG - Calling on_field_name with data[55:63]
2025-05-20 22:32:31,778 - multipart.multipart - DEBUG - Calling on_field_data with data[64:71]
2025-05-20 22:32:31,778 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-20 22:32:31,778 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-20 22:32:31,778 - multipart.multipart - DEBUG - Calling on_field_name with data[72:77]
2025-05-20 22:32:31,779 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-20 22:32:31,779 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-20 22:32:31,779 - multipart.multipart - DEBUG - Calling on_field_name with data[79:88]
2025-05-20 22:32:31,779 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-20 22:32:31,779 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-20 22:32:31,779 - multipart.multipart - DEBUG - Calling on_field_name with data[90:103]
2025-05-20 22:32:31,780 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-20 22:32:31,780 - multipart.multipart - DEBUG - Calling on_end with no data
2025-05-20 22:32:31,781 - app.routers.auth - INFO - Login request received for email: <EMAIL>
2025-05-20 22:32:32,972 - app.routers.auth - INFO - Login successful for user: <EMAIL>
2025-05-20 22:32:57,590 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-20 22:32:57,590 - multipart.multipart - DEBUG - Calling on_field_name with data[0:10]
2025-05-20 22:32:57,590 - multipart.multipart - DEBUG - Calling on_field_data with data[11:19]
2025-05-20 22:32:57,590 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-20 22:32:57,590 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-20 22:32:57,591 - multipart.multipart - DEBUG - Calling on_field_name with data[20:28]
2025-05-20 22:32:57,591 - multipart.multipart - DEBUG - Calling on_field_data with data[29:62]
2025-05-20 22:32:57,591 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-20 22:32:57,591 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-20 22:32:57,591 - multipart.multipart - DEBUG - Calling on_field_name with data[63:71]
2025-05-20 22:32:57,591 - multipart.multipart - DEBUG - Calling on_field_data with data[72:79]
2025-05-20 22:32:57,592 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-20 22:32:57,592 - multipart.multipart - DEBUG - Calling on_end with no data
2025-05-20 22:32:57,594 - app.routers.auth - INFO - Login request received for email: <EMAIL>
2025-05-20 22:32:58,942 - app.routers.auth - INFO - Login successful for user: <EMAIL>
2025-05-20 22:33:55,960 - multipart.multipart - DEBUG - Calling on_part_begin with no data
2025-05-20 22:33:55,960 - multipart.multipart - DEBUG - Calling on_header_field with data[42:61]
2025-05-20 22:33:55,960 - multipart.multipart - DEBUG - Calling on_header_value with data[63:109]
2025-05-20 22:33:55,960 - multipart.multipart - DEBUG - Calling on_header_end with no data
2025-05-20 22:33:55,960 - multipart.multipart - DEBUG - Calling on_header_field with data[111:123]
2025-05-20 22:33:55,960 - multipart.multipart - DEBUG - Calling on_header_value with data[125:133]
2025-05-20 22:33:55,960 - multipart.multipart - DEBUG - Calling on_header_end with no data
2025-05-20 22:33:55,960 - multipart.multipart - DEBUG - Calling on_headers_finished with no data
2025-05-20 22:33:55,961 - multipart.multipart - DEBUG - Calling on_part_data with data[137:13379]
2025-05-20 22:33:55,961 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:55,963 - multipart.multipart - DEBUG - Calling on_part_data with data[13381:54884]
2025-05-20 22:33:55,963 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:55,964 - multipart.multipart - DEBUG - Calling on_part_data with data[54886:98304]
2025-05-20 22:33:55,966 - multipart.multipart - DEBUG - Calling on_part_data with data[0:65165]
2025-05-20 22:33:55,966 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:55,968 - multipart.multipart - DEBUG - Calling on_part_data with data[65167:144952]
2025-05-20 22:33:55,969 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:55,969 - multipart.multipart - DEBUG - Calling on_part_data with data[144954:162625]
2025-05-20 22:33:55,969 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:55,972 - multipart.multipart - DEBUG - Calling on_part_data with data[162627:262144]
2025-05-20 22:33:55,974 - multipart.multipart - DEBUG - Calling on_part_data with data[0:35207]
2025-05-20 22:33:55,975 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:55,975 - multipart.multipart - DEBUG - Calling on_part_data with data[35209:55795]
2025-05-20 22:33:55,976 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:55,978 - multipart.multipart - DEBUG - Calling on_part_data with data[55797:143327]
2025-05-20 22:33:55,979 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:55,979 - multipart.multipart - DEBUG - Calling on_part_data with data[143329:150924]
2025-05-20 22:33:55,979 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:55,979 - multipart.multipart - DEBUG - Calling on_part_data with data[150926:153914]
2025-05-20 22:33:55,979 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:55,980 - multipart.multipart - DEBUG - Calling on_part_data with data[153916:164898]
2025-05-20 22:33:55,980 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:55,982 - multipart.multipart - DEBUG - Calling on_part_data with data[164900:245760]
2025-05-20 22:33:55,985 - multipart.multipart - DEBUG - Calling on_part_data with data[0:38134]
2025-05-20 22:33:55,985 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:55,986 - multipart.multipart - DEBUG - Calling on_part_data with data[38136:69443]
2025-05-20 22:33:55,986 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:55,988 - multipart.multipart - DEBUG - Calling on_part_data with data[69445:115921]
2025-05-20 22:33:55,988 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:55,996 - multipart.multipart - DEBUG - Calling on_part_data with data[115923:262107]
2025-05-20 22:33:55,997 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:55,997 - multipart.multipart - DEBUG - Calling on_part_data with data[262109:262144]
2025-05-20 22:33:56,000 - multipart.multipart - DEBUG - Calling on_part_data with data[0:47299]
2025-05-20 22:33:56,001 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:56,003 - multipart.multipart - DEBUG - Calling on_part_data with data[47301:73609]
2025-05-20 22:33:56,003 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:56,005 - multipart.multipart - DEBUG - Calling on_part_data with data[73611:139117]
2025-05-20 22:33:56,005 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:56,006 - multipart.multipart - DEBUG - Calling on_part_data with data[139119:158447]
2025-05-20 22:33:56,006 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:56,009 - multipart.multipart - DEBUG - Calling on_part_data with data[158449:248029]
2025-05-20 22:33:56,009 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:56,010 - multipart.multipart - DEBUG - Calling on_part_data with data[248031:262144]
2025-05-20 22:33:56,030 - multipart.multipart - DEBUG - Calling on_part_data with data[0:24320]
2025-05-20 22:33:56,031 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:56,032 - multipart.multipart - DEBUG - Calling on_part_data with data[24322:60232]
2025-05-20 22:33:56,032 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:56,033 - multipart.multipart - DEBUG - Calling on_part_data with data[60234:79876]
2025-05-20 22:33:56,033 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:56,035 - multipart.multipart - DEBUG - Calling on_part_data with data[79878:136847]
2025-05-20 22:33:56,035 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:56,037 - multipart.multipart - DEBUG - Calling on_part_data with data[136849:188805]
2025-05-20 22:33:56,037 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:56,040 - multipart.multipart - DEBUG - Calling on_part_data with data[188807:248470]
2025-05-20 22:33:56,040 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:56,041 - multipart.multipart - DEBUG - Calling on_part_data with data[248472:262144]
2025-05-20 22:33:56,051 - multipart.multipart - DEBUG - Calling on_part_data with data[0:106837]
2025-05-20 22:33:56,051 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:56,052 - multipart.multipart - DEBUG - Calling on_part_data with data[106839:198441]
2025-05-20 22:33:56,053 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:56,053 - multipart.multipart - DEBUG - Calling on_part_data with data[198443:219105]
2025-05-20 22:33:56,053 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:56,054 - multipart.multipart - DEBUG - Calling on_part_data with data[219107:253716]
2025-05-20 22:33:56,054 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:56,055 - multipart.multipart - DEBUG - Calling on_part_data with data[253718:262144]
2025-05-20 22:33:56,062 - multipart.multipart - DEBUG - Calling on_part_data with data[0:10435]
2025-05-20 22:33:56,062 - multipart.multipart - DEBUG - Calling on_part_data with data[0:2]
2025-05-20 22:33:56,062 - multipart.multipart - DEBUG - Calling on_part_end with no data
2025-05-20 22:33:56,062 - multipart.multipart - DEBUG - Calling on_end with no data
2025-05-20 23:56:21,976 - app.routers.auth - INFO - Verification code request received for email: <EMAIL>
2025-05-20 23:56:37,546 - multipart.multipart - DEBUG - Calling on_part_begin with no data
2025-05-20 23:56:37,546 - multipart.multipart - DEBUG - Calling on_header_field with data[42:61]
2025-05-20 23:56:37,547 - multipart.multipart - DEBUG - Calling on_header_value with data[63:89]
2025-05-20 23:56:37,547 - multipart.multipart - DEBUG - Calling on_header_end with no data
2025-05-20 23:56:37,547 - multipart.multipart - DEBUG - Calling on_headers_finished with no data
2025-05-20 23:56:37,547 - multipart.multipart - DEBUG - Calling on_part_data with data[93:124]
2025-05-20 23:56:37,547 - multipart.multipart - DEBUG - Calling on_part_end with no data
2025-05-20 23:56:37,547 - multipart.multipart - DEBUG - Calling on_part_begin with no data
2025-05-20 23:56:37,547 - multipart.multipart - DEBUG - Calling on_header_field with data[168:187]
2025-05-20 23:56:37,547 - multipart.multipart - DEBUG - Calling on_header_value with data[189:215]
2025-05-20 23:56:37,547 - multipart.multipart - DEBUG - Calling on_header_end with no data
2025-05-20 23:56:37,547 - multipart.multipart - DEBUG - Calling on_headers_finished with no data
2025-05-20 23:56:37,547 - multipart.multipart - DEBUG - Calling on_part_data with data[219:226]
2025-05-20 23:56:37,548 - multipart.multipart - DEBUG - Calling on_part_end with no data
2025-05-20 23:56:37,548 - multipart.multipart - DEBUG - Calling on_end with no data
2025-05-20 23:56:37,549 - app.routers.auth - INFO - Login request received for email: <EMAIL>
2025-05-20 23:56:40,016 - app.routers.auth - INFO - Login successful for user: <EMAIL>
2025-05-21 22:57:17,373 - app.routers.auth - INFO - Verification code request received for email: <EMAIL>
2025-05-21 22:57:19,170 - app.utils.email - INFO - Attempting to send email to: <EMAIL>
2025-05-21 22:57:19,171 - app.utils.email - INFO - Using SMTP settings: Host=smtp.gmail.com, Port=587, User=<EMAIL>
2025-05-21 22:57:19,486 - app.utils.email - INFO - Using SMTP authentication
2025-05-21 22:57:22,510 - app.utils.email - INFO - Email send response: Status=250, Error=None
2025-05-21 22:57:22,511 - app.routers.auth - INFO - Verification code sent to new user: <EMAIL>
2025-05-21 23:13:51,112 - app.routers.auth - INFO - Set password request received for email: <EMAIL>
2025-05-21 23:17:20,926 - multipart.multipart - DEBUG - Calling on_part_begin with no data
2025-05-21 23:17:20,927 - multipart.multipart - DEBUG - Calling on_header_field with data[42:61]
2025-05-21 23:17:20,927 - multipart.multipart - DEBUG - Calling on_header_value with data[63:89]
2025-05-21 23:17:20,927 - multipart.multipart - DEBUG - Calling on_header_end with no data
2025-05-21 23:17:20,928 - multipart.multipart - DEBUG - Calling on_headers_finished with no data
2025-05-21 23:17:20,928 - multipart.multipart - DEBUG - Calling on_part_data with data[93:114]
2025-05-21 23:17:20,928 - multipart.multipart - DEBUG - Calling on_part_end with no data
2025-05-21 23:17:20,928 - multipart.multipart - DEBUG - Calling on_part_begin with no data
2025-05-21 23:17:20,928 - multipart.multipart - DEBUG - Calling on_header_field with data[158:177]
2025-05-21 23:17:20,928 - multipart.multipart - DEBUG - Calling on_header_value with data[179:205]
2025-05-21 23:17:20,928 - multipart.multipart - DEBUG - Calling on_header_end with no data
2025-05-21 23:17:20,928 - multipart.multipart - DEBUG - Calling on_headers_finished with no data
2025-05-21 23:17:20,929 - multipart.multipart - DEBUG - Calling on_part_data with data[209:218]
2025-05-21 23:17:20,929 - multipart.multipart - DEBUG - Calling on_part_end with no data
2025-05-21 23:17:20,929 - multipart.multipart - DEBUG - Calling on_end with no data
2025-05-21 23:17:20,931 - app.routers.auth - INFO - Login request received for email: <EMAIL>
2025-05-21 23:17:22,599 - app.routers.auth - ERROR - User not verified: <EMAIL>
2025-05-21 23:17:30,951 - multipart.multipart - DEBUG - Calling on_part_begin with no data
2025-05-21 23:17:30,951 - multipart.multipart - DEBUG - Calling on_header_field with data[42:61]
2025-05-21 23:17:30,951 - multipart.multipart - DEBUG - Calling on_header_value with data[63:89]
2025-05-21 23:17:30,952 - multipart.multipart - DEBUG - Calling on_header_end with no data
2025-05-21 23:17:30,952 - multipart.multipart - DEBUG - Calling on_headers_finished with no data
2025-05-21 23:17:30,952 - multipart.multipart - DEBUG - Calling on_part_data with data[93:114]
2025-05-21 23:17:30,952 - multipart.multipart - DEBUG - Calling on_part_end with no data
2025-05-21 23:17:30,952 - multipart.multipart - DEBUG - Calling on_part_begin with no data
2025-05-21 23:17:30,952 - multipart.multipart - DEBUG - Calling on_header_field with data[158:177]
2025-05-21 23:17:30,952 - multipart.multipart - DEBUG - Calling on_header_value with data[179:205]
2025-05-21 23:17:30,952 - multipart.multipart - DEBUG - Calling on_header_end with no data
2025-05-21 23:17:30,952 - multipart.multipart - DEBUG - Calling on_headers_finished with no data
2025-05-21 23:17:30,953 - multipart.multipart - DEBUG - Calling on_part_data with data[209:218]
2025-05-21 23:17:30,953 - multipart.multipart - DEBUG - Calling on_part_end with no data
2025-05-21 23:17:30,953 - multipart.multipart - DEBUG - Calling on_end with no data
2025-05-21 23:17:30,955 - app.routers.auth - INFO - Login request received for email: <EMAIL>
2025-05-21 23:17:31,186 - app.routers.auth - ERROR - User not verified: <EMAIL>
2025-05-22 10:43:13,550 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-22 10:43:13,550 - multipart.multipart - DEBUG - Calling on_field_name with data[0:10]
2025-05-22 10:43:13,550 - multipart.multipart - DEBUG - Calling on_field_data with data[11:19]
2025-05-22 10:43:13,550 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-22 10:43:13,550 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-22 10:43:13,550 - multipart.multipart - DEBUG - Calling on_field_name with data[20:28]
2025-05-22 10:43:13,550 - multipart.multipart - DEBUG - Calling on_field_data with data[29:36]
2025-05-22 10:43:13,550 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-22 10:43:13,550 - multipart.multipart - DEBUG - Calling on_field_start with no data
2025-05-22 10:43:13,550 - multipart.multipart - DEBUG - Calling on_field_name with data[37:45]
2025-05-22 10:43:13,550 - multipart.multipart - DEBUG - Calling on_field_data with data[46:50]
2025-05-22 10:43:13,550 - multipart.multipart - DEBUG - Calling on_field_end with no data
2025-05-22 10:43:13,550 - multipart.multipart - DEBUG - Calling on_end with no data
2025-05-22 10:43:13,559 - app.routers.auth - INFO - Login request received for email: mariyam
2025-05-22 10:43:30,762 - app.routers.auth - INFO - Verification code request received for email: <EMAIL>
2025-05-22 10:43:34,465 - app.utils.email - INFO - Attempting to send email to: <EMAIL>
2025-05-22 10:43:34,465 - app.utils.email - INFO - Using SMTP settings: Host=smtp.gmail.com, Port=587, User=<EMAIL>
2025-05-22 10:43:34,465 - app.utils.email - INFO - Using SMTP authentication
2025-05-22 10:43:37,804 - app.utils.email - INFO - Email send response: Status=250, Error=None
2025-05-22 10:43:37,804 - app.routers.auth - INFO - Verification code sent to new user: <EMAIL>
2025-05-22 10:44:47,326 - app.routers.auth - INFO - Verification code request received for email: <EMAIL>
2025-05-22 10:44:48,086 - app.utils.email - INFO - Attempting to send email to: <EMAIL>
2025-05-22 10:44:48,086 - app.utils.email - INFO - Using SMTP settings: Host=smtp.gmail.com, Port=587, User=<EMAIL>
2025-05-22 10:44:48,086 - app.utils.email - INFO - Using SMTP authentication
2025-05-22 10:44:51,129 - app.utils.email - INFO - Email send response: Status=250, Error=None
2025-05-22 10:44:51,129 - app.routers.auth - INFO - Verification code sent to existing user: <EMAIL>
