import os
import pandas as pd
import json
from sqlalchemy.orm import Session

from app.database import SessionLocal, Upload, SiteData
from app.services.kml_generator import generate_kml_files
from app.config import OUTPUT_DIR

def process_csv_file(upload_id, file_path):
    """
    Process a CSV file and generate KML/KMZ files.
    
    Args:
        upload_id: The ID of the upload record
        file_path: The path to the CSV file
    """
    # Create a database session
    db = SessionLocal()
    
    try:
        # Update the upload status to "processing"
        upload = db.query(Upload).filter(Upload.id == upload_id).first()
        upload.status = "processing"
        db.commit()
        
        # Read the CSV file
        try:
            df = pd.read_csv(file_path)
        except Exception as e:
            upload.status = "failed"
            upload.error_message = f"Failed to read CSV file: {str(e)}"
            db.commit()
            return
        
        # Store the data in the database
        try:
            # Convert each row to a dictionary and store in the database
            for _, row in df.iterrows():
                # Convert row to dict, handling NaN values
                row_dict = row.to_dict()
                for key, value in row_dict.items():
                    if pd.isna(value):
                        row_dict[key] = None
                
                # Create a new site data record
                site_data = SiteData(
                    upload_id=upload_id,
                    data=row_dict
                )
                db.add(site_data)
            
            db.commit()
        except Exception as e:
            upload.status = "failed"
            upload.error_message = f"Failed to store data in database: {str(e)}"
            db.commit()
            return
        
        # Create output directory for this upload
        output_dir = os.path.join(OUTPUT_DIR, str(upload_id))
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate KML/KMZ files
        try:
            generate_kml_files(upload_id, df, output_dir, db)
            
            # Update the upload status to "completed"
            upload.status = "completed"
            db.commit()
        except Exception as e:
            upload.status = "failed"
            upload.error_message = f"Failed to generate KML/KMZ files: {str(e)}"
            db.commit()
    
    except Exception as e:
        # Handle any unexpected errors
        try:
            upload = db.query(Upload).filter(Upload.id == upload_id).first()
            upload.status = "failed"
            upload.error_message = f"Unexpected error: {str(e)}"
            db.commit()
        except:
            pass
    
    finally:
        # Close the database session
        db.close()
