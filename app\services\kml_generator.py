import os
import math
import simplekml
import pandas as pd
from sqlalchemy.orm import Session

from app.database import OutputFile
from app.config import DEFAULT_RADIUS_MAPPING

def generate_kml_files(upload_id, df, output_dir, db):
    """
    Generate KML/KMZ files from a pandas DataFrame.
    
    Args:
        upload_id: The ID of the upload record
        df: The pandas DataFrame containing the CSV data
        output_dir: The directory to save the output files
        db: The database session
    """
    # Generate cell analysis KMZ file
    cell_analysis_path = os.path.join(output_dir, "cell_analysis.kmz")
    generate_cell_analysis_kmz(df, cell_analysis_path)
    
    # Record the output file in the database
    output_file = OutputFile(
        upload_id=upload_id,
        filename="cell_analysis.kmz",
        file_type="kmz",
        file_path=cell_analysis_path
    )
    db.add(output_file)
    
    # Generate site points KML file
    site_points_path = os.path.join(output_dir, "site_points.kml")
    generate_site_points_kml(df, site_points_path)
    
    # Record the output file in the database
    output_file = OutputFile(
        upload_id=upload_id,
        filename="site_points.kml",
        file_type="kml",
        file_path=site_points_path
    )
    db.add(output_file)
    
    db.commit()

def generate_pie_wedge(kml, lat, lon, azimuth, radius, cell_name, color, cell_data):
    """
    Generate a pie wedge polygon for a cell site.
    
    Args:
        kml: The simplekml.Kml object
        lat: The latitude of the cell site
        lon: The longitude of the cell site
        azimuth: The azimuth angle of the cell site
        radius: The radius of the cell coverage
        cell_name: The name of the cell
        color: The color of the polygon
        cell_data: A dictionary of cell data for the description
    """
    num_points = 36
    start_angle = azimuth - 20
    end_angle = azimuth + 20
    step_angle = (end_angle - start_angle) / num_points
    coords = [(lon, lat)]

    for i in range(num_points + 1):
        angle = math.radians(start_angle + i * step_angle)
        lat_offset = lat + (radius * math.cos(angle)) / 111.32
        lon_offset = lon + (radius * math.sin(angle)) / (111.32 * math.cos(math.radians(lat)))
        coords.append((lon_offset, lat_offset))

    polygon = kml.newpolygon(name=cell_name, outerboundaryis=coords)
    polygon.style.polystyle.color = color
    polygon.style.polystyle.outline = 1

    description = f"""
    <table border="1">
        <tr><th>Field</th><th>Value</th></tr>
        {"".join([f"<tr><td>{key}</td><td>{value}</td></tr>" for key, value in cell_data.items()])}
    </table>
    """
    polygon.description = description

def get_color(dl_prb):
    """
    Get the color for a cell based on DL PRB rate.
    
    Args:
        dl_prb: The DL PRB rate
        
    Returns:
        A string representing the color in KML format
    """
    if dl_prb >= 0.85:
        return "7f0000ff"  # Red (Alpha=7F, Blue=00, Green=00, Red=FF)
    elif 0.70 <= dl_prb < 0.85:
        return "7f00ffff"  # Yellow (Alpha=7F, Blue=00, Green=FF, Red=FF)
    else:
        return "7f00ff00"  # Green (Alpha=7F, Blue=00, Green=FF, Red=00)

def generate_cell_analysis_kmz(df, output_path):
    """
    Generate a KMZ file with cell analysis data.
    
    Args:
        df: The pandas DataFrame containing the CSV data
        output_path: The path to save the KMZ file
    """
    # Initialize KML
    kml = simplekml.Kml()
    
    # Process each row
    for _, row in df.iterrows():
        try:
            band = row["Band"]
            dl_prb = row["DL PRB Rate"]
            color = get_color(dl_prb)
            radius = DEFAULT_RADIUS_MAPPING.get(band, 0.15)  # Default radius for unknown bands
            
            # Create a dictionary of cell data, handling NaN values
            cell_data = {}
            for key, value in row.items():
                if pd.notnull(value):
                    cell_data[key] = value
                else:
                    cell_data[key] = "N/A"
            
            generate_pie_wedge(
                kml,
                lat=row["Latitude"],
                lon=row["Longitude"],
                azimuth=row["Azimuth"],
                radius=radius,
                cell_name=row["Cell Name"],
                color=color,
                cell_data=cell_data,
            )
        except Exception as e:
            print(f"Error processing row: {row}. Error: {e}")
    
    # Save KMZ
    kml.savekmz(output_path)

def get_icon_url(category):
    """
    Get the icon URL for a site based on its category.
    
    Args:
        category: The category of the site
        
    Returns:
        A string representing the URL of the icon
    """
    icon_mapping = {
        "Platinum": "http://maps.google.com/mapfiles/kml/paddle/blu-diamond.png",
        "Gold": "http://maps.google.com/mapfiles/kml/paddle/ylw-diamond.png",
        "Silver": "http://maps.google.com/mapfiles/kml/paddle/wht-diamond.png",
        "Bronze": "http://maps.google.com/mapfiles/kml/paddle/red-diamond.png",
        "Strategic": "http://maps.google.com/mapfiles/kml/paddle/ltblu-diamond.png",
    }
    return icon_mapping.get(category, "http://maps.google.com/mapfiles/kml/paddle/E.png")

def generate_site_points_kml(df, output_path):
    """
    Generate a KML file with site points.
    
    Args:
        df: The pandas DataFrame containing the CSV data
        output_path: The path to save the KML file
    """
    # Initialize KML
    kml = simplekml.Kml()
    
    # Filter out duplicates based on Longitude and Latitude to get unique sites
    unique_sites = df.drop_duplicates(subset=["Longitude", "Latitude"])
    
    # Process each unique site
    for _, row in unique_sites.iterrows():
        try:
            # Extract values
            category = row["Category"]
            enodeb = row["eNodeB"]
            lon = row["Longitude"]
            lat = row["Latitude"]
            
            # Ensure values are not missing
            if pd.isnull(category) or pd.isnull(lon) or pd.isnull(lat):
                continue
            
            # Get the icon URL based on the category
            icon_url = get_icon_url(category)
            
            # Create a point with the category-specific marker
            point = kml.newpoint(
                name=f"{enodeb}",
                coords=[(lon, lat)]
            )
            point.style.iconstyle.icon.href = icon_url  # Set icon based on category
            point.style.iconstyle.scale = 1  # Normal size marker
            
            # Create a dictionary of site data, handling NaN values
            site_data = {}
            for key, value in row.items():
                if pd.notnull(value):
                    site_data[key] = value
                else:
                    site_data[key] = "N/A"
            
            # Description for popup info in Google Earth
            description = f"""
            <table border="1">
                <tr><th>Field</th><th>Value</th></tr>
                {"".join([f"<tr><td>{key}</td><td>{value}</td></tr>" for key, value in site_data.items()])}
            </table>
            """
            point.description = description
            
        except Exception as e:
            print(f"Error processing row: {row}. Error: {e}")
    
    # Save KML
    kml.save(output_path)
