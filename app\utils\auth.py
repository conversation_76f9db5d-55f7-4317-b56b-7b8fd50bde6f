from datetime import datetime, timedelta, timezone
from typing import Optional
from uuid import UUI<PERSON>
from jose import jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON>2<PERSON><PERSON>wordBearer
from sqlalchemy.orm import Session
from passlib.context import Crypt<PERSON>ontext

from app.config import SECRET_KEY, ALGORITHM, ACCESS_TOKEN_EXPIRE_MINUTES, VERIFICATION_CODE_EXPIRE_MINUTES
from app.database import get_db, User
from app.models.auth import TokenData

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Use the login endpoint for OAuth2 password flow
# The tokenUrl should match the path in the router
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/login")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against a hash.

    Args:
        plain_password: The plain text password
        hashed_password: The hashed password

    Returns:
        True if the password matches the hash, False otherwise
    """
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """
    Hash a password.

    Args:
        password: The plain text password

    Returns:
        The hashed password
    """
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT access token.

    Args:
        data: The data to encode in the token
        expires_delta: The token expiration time

    Returns:
        The encoded JWT token
    """
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

    return encoded_jwt

def verify_token(token: str) -> TokenData:
    """
    Verify a JWT token and extract the token data.

    Args:
        token: The JWT token to verify

    Returns:
        The token data

    Raises:
        HTTPException: If the token is invalid
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        user_id: str = payload.get("user_id")

        if email is None or user_id is None:
            raise credentials_exception

        return TokenData(email=email, user_id=UUID(user_id))
    except:
        raise credentials_exception

def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)) -> User:
    """
    Get the current user from the JWT token.

    Args:
        token: The JWT token
        db: The database session

    Returns:
        The current user

    Raises:
        HTTPException: If the user is not found or inactive
    """
    token_data = verify_token(token)
    user = db.query(User).filter(User.email == token_data.email).first()

    if user is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user",
        )

    return user

def is_verification_code_expired(code_created_at: datetime) -> bool:
    """
    Check if a verification code is expired.

    Args:
        code_created_at: The time when the code was created

    Returns:
        True if the code is expired, False otherwise
    """
    if code_created_at is None:
        return True

    # Ensure code_created_at has timezone info
    if code_created_at.tzinfo is None:
        # If it's naive, assume it's in UTC
        code_created_at = code_created_at.replace(tzinfo=timezone.utc)

    expiration_time = code_created_at + timedelta(minutes=VERIFICATION_CODE_EXPIRE_MINUTES)
    return datetime.now(timezone.utc) > expiration_time
