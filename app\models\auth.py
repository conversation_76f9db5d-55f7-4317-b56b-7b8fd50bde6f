from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import BaseModel, Field

class UserBase(BaseModel):
    """Base model for user data."""
    email: str

class UserCreate(UserBase):
    """Model for user creation."""
    pass

class UserVerify(UserBase):
    """Model for user verification."""
    code: str

class SetPasswordRequest(UserBase):
    """Model for setting a password after verification."""
    password: str

class UserLogin(UserBase):
    """Model for user login."""
    password: str

class UserResponse(UserBase):
    """Response model for user information."""
    id: UUID
    is_active: bool
    created_at: datetime
    last_login: Optional[datetime] = None

    class Config:
        from_attributes = True

class Token(BaseModel):
    """Model for authentication token."""
    access_token: str
    token_type: str

class TokenData(BaseModel):
    """Model for token data."""
    email: Optional[str] = None
    user_id: Optional[UUID] = None
