[tool.poetry]
name = "kml-automation"
version = "0.1.0"
description = "KML Automation API for generating KML/KMZ files from CSV data"
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.104.1"
uvicorn = "^0.24.0"
sqlalchemy = "^2.0.23"
pydantic = "^2.4.2"
pandas = "^2.1.3"
simplekml = "^1.3.6"
python-multipart = "^0.0.6"
psycopg2-binary = "^2.9.9"
python-dotenv = "^1.0.0"
asyncpg = "^0.29.0"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
emails = "^0.6"
jinja2 = "^3.1.2"

[tool.poetry.dev-dependencies]
pytest = "^7.4.0"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
