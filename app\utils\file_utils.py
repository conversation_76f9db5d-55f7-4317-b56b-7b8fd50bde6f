import os
import shutil
from fastapi import UploadFile

async def save_upload_file(upload_file: UploadFile, destination: str) -> str:
    """
    Save an uploaded file to the specified destination.

    Args:
        upload_file: The uploaded file
        destination: The destination path

    Returns:
        The path to the saved file
    """
    # Ensure the directory exists
    os.makedirs(os.path.dirname(destination), exist_ok=True)

    # Save the file
    with open(destination, "wb") as buffer:
        shutil.copyfileobj(upload_file.file, buffer)

    return destination

def delete_file(file_path: str) -> bool:
    """
    Delete a file.

    Args:
        file_path: The path to the file

    Returns:
        True if the file was deleted, False otherwise
    """
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
        return False
    except Exception as e:
        print(f"Error deleting file {file_path}: {e}")
        return False

def clean_directory(directory_path: str) -> bool:
    """
    Clean a directory by removing all files and subdirectories and recreating it.

    Args:
        directory_path: The path to the directory

    Returns:
        True if the directory was cleaned, False otherwise
    """
    try:
        if os.path.exists(directory_path):
            shutil.rmtree(directory_path)
        os.makedirs(directory_path, exist_ok=True)
        return True
    except Exception as e:
        print(f"Error cleaning directory {directory_path}: {e}")
        return False
