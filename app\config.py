import os
from dotenv import load_dotenv
from datetime import timedelta

# Load environment variables from .env file
load_dotenv()

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://kml_owner:<EMAIL>/kml?sslmode=require")

# File storage configuration
UPLOAD_DIR = os.getenv("UPLOAD_DIR", "uploads")
OUTPUT_DIR = os.getenv("OUTPUT_DIR", "outputs")

# Ensure directories exist
os.makedirs(UPLOAD_DIR, exist_ok=True)
os.makedirs(OUTPUT_DIR, exist_ok=True)

# API configuration
API_HOST = os.getenv("API_HOST", "0.0.0.0")
API_PORT = int(os.getenv("API_PORT", "8000"))
API_RELOAD = os.getenv("API_RELOAD", "True").lower() == "true"

# KML generation configuration
DEFAULT_RADIUS_MAPPING = {
    900: 0.2,
    1800: 0.17,
    2100: 0.15
}

# Authentication configuration
SECRET_KEY = os.getenv("SECRET_KEY", "09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
VERIFICATION_CODE_EXPIRE_MINUTES = int(os.getenv("VERIFICATION_CODE_EXPIRE_MINUTES", "15"))

# Email configuration
SMTP_TLS = os.getenv("SMTP_TLS", "True").lower() == "true"
SMTP_PORT = int(os.getenv("SMTP_PORT", "587"))
SMTP_HOST = os.getenv("SMTP_HOST", "smtp.gmail.com")
SMTP_USER = os.getenv("SMTP_USER", "<EMAIL>")
SMTP_PASSWORD = os.getenv("SMTP_PASSWORD", "your-app-password")
EMAILS_FROM_EMAIL = os.getenv("EMAILS_FROM_EMAIL", SMTP_USER)
EMAILS_FROM_NAME = os.getenv("EMAILS_FROM_NAME", "KML Automation")

# Frontend URL for email verification links
FRONTEND_URL = os.getenv("FRONTEND_URL", "http://localhost:3000")
