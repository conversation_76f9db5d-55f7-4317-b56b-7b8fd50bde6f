import os
import uuid
import pandas as pd
from fastapi import APIRouter, Depends, File, UploadFile, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
import json

from app.database import get_db, Upload, SiteData, User
from app.models.upload import UploadResponse
from app.services.csv_processor import process_csv_file
from app.config import UPLOAD_DIR
from app.utils.auth import get_current_user

router = APIRouter(
    prefix="/upload",
    tags=["upload"],
    responses={404: {"description": "Not found"}},
)

@router.post("/", response_model=UploadResponse)
async def upload_csv(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Upload a CSV file for KML/KMZ generation.

    The file will be processed asynchronously, and the status can be checked
    using the /status/{upload_id} endpoint.
    """
    # Validate file type
    if not file.filename.endswith('.csv'):
        raise HTTPException(status_code=400, detail="Only CSV files are accepted")

    # Generate a unique ID for this upload
    upload_id = uuid.uuid4()

    # Create a directory for this upload if it doesn't exist
    upload_dir = os.path.join(UPLOAD_DIR, str(upload_id))
    os.makedirs(upload_dir, exist_ok=True)

    # Save the uploaded file
    file_path = os.path.join(upload_dir, file.filename)
    with open(file_path, "wb") as f:
        f.write(await file.read())

    # Create a new upload record in the database
    upload = Upload(
        id=upload_id,
        user_id=current_user.id,
        filename=file.filename,
        status="pending"
    )
    db.add(upload)
    db.commit()

    # Process the file in the background
    background_tasks.add_task(
        process_csv_file,
        upload_id=upload_id,
        file_path=file_path
    )

    return UploadResponse(
        upload_id=upload_id,
        filename=file.filename,
        status="pending"
    )
