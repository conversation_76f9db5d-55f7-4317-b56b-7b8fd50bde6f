from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager

from app.routers import upload, status, download, auth
from app.database import create_tables, cleanup_database
from app.config import UPLOAD_DIR, OUTPUT_DIR
from app.utils.file_utils import clean_directory

def cleanup_files():
    """Clean up all uploaded and generated files."""
    # Clean up upload directory
    clean_directory(UPLOAD_DIR)
    print(f"Cleaned up upload directory: {UPLOAD_DIR}")

    # Clean up output directory
    clean_directory(OUTPUT_DIR)
    print(f"Cleaned up output directory: {OUTPUT_DIR}")

@asynccontextmanager
async def lifespan(_: FastAPI):
    """Lifespan event handler for FastAPI application."""
    # Startup: Create tables and clean up old data
    create_tables()
    cleanup_database()
    cleanup_files()
    print("Application startup: Database tables created and old data cleaned up.")

    yield

    # Shutdown: No specific actions needed
    print("Application shutdown.")

app = FastAPI(
    title="KML Automation API",
    description="API for generating KML/KMZ files from CSV data",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# Include routers
app.include_router(auth.router)
app.include_router(upload.router)
app.include_router(status.router)
app.include_router(download.router)

@app.get("/")
async def root():
    """Root endpoint to check if the API is running."""
    return {"message": "KML Automation API is running"}

@app.exception_handler(HTTPException)
async def http_exception_handler(_, exc):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail},
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
