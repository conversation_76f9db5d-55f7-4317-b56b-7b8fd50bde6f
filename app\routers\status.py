from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from uuid import UUID

from app.database import get_db, Upload, OutputFile, User
from app.models.upload import StatusResponse
from app.utils.auth import get_current_user

router = APIRouter(
    prefix="/status",
    tags=["status"],
    responses={404: {"description": "Not found"}},
)

@router.get("/{upload_id}", response_model=StatusResponse)
async def get_upload_status(
    upload_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get the status of a CSV file upload and processing.

    Returns the current status, and if completed, links to download the generated files.
    """
    # Query the upload record
    upload = db.query(Upload).filter(Upload.id == upload_id, Upload.user_id == current_user.id).first()

    if not upload:
        raise HTTPException(status_code=404, detail=f"Upload with ID {upload_id} not found or does not belong to you")

    # Prepare the response
    response = StatusResponse(
        upload_id=upload.id,
        status=upload.status,
        filename=upload.filename,
        upload_time=upload.upload_time,
        error_message=upload.error_message,
        output_files=[]
    )

    # If the upload is completed, include links to the output files
    if upload.status == "completed":
        output_files = db.query(OutputFile).filter(OutputFile.upload_id == upload_id).all()

        for output_file in output_files:
            response.output_files.append({
                "id": output_file.id,
                "filename": output_file.filename,
                "file_type": output_file.file_type,
                "created_at": output_file.created_at,
                "download_url": f"/download/{output_file.id}"
            })

    return response
