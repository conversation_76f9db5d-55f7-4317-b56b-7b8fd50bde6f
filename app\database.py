import uuid
from datetime import datetime, timezone
from sqlalchemy import create_engine, Column, String, DateTime, ForeignKey, Text, Boolean
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship

from app.config import DATABASE_URL

# Create SQLAlchemy engine and session
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Define database models
class User(Base):
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String, unique=True, index=True, nullable=False)
    is_active = Column(Boolean, default=False)
    hashed_password = Column(String, nullable=True)  # Added password field
    verification_code = Column(String, nullable=True)
    code_created_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    last_login = Column(DateTime, nullable=True)

    # Relationships
    uploads = relationship("Upload", back_populates="user", cascade="all, delete-orphan")

class Upload(Base):
    __tablename__ = "uploads"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    filename = Column(String, nullable=False)
    upload_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    status = Column(String, default="pending")  # pending, processing, completed, failed
    error_message = Column(Text, nullable=True)

    # Relationships
    user = relationship("User", back_populates="uploads")
    site_data = relationship("SiteData", back_populates="upload", cascade="all, delete-orphan")
    output_files = relationship("OutputFile", back_populates="upload", cascade="all, delete-orphan")

class SiteData(Base):
    __tablename__ = "site_data"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    upload_id = Column(UUID(as_uuid=True), ForeignKey("uploads.id"), nullable=False)
    data = Column(JSONB, nullable=False)

    # Relationships
    upload = relationship("Upload", back_populates="site_data")

class OutputFile(Base):
    __tablename__ = "output_files"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    upload_id = Column(UUID(as_uuid=True), ForeignKey("uploads.id"), nullable=False)
    filename = Column(String, nullable=False)
    file_type = Column(String, nullable=False)  # kml or kmz
    file_path = Column(String, nullable=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    upload = relationship("Upload", back_populates="output_files")

def create_tables():
    """Create all tables in the database."""
    # Drop all tables first to ensure schema is up to date
    Base.metadata.drop_all(bind=engine)
    # Create all tables
    Base.metadata.create_all(bind=engine)
    print("Database tables dropped and recreated.")

def get_db():
    """Get a database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def cleanup_database():
    """Clean up all data from the database except users."""
    db = SessionLocal()
    try:
        # Delete all output files
        db.query(OutputFile).delete()

        # Delete all site data
        db.query(SiteData).delete()

        # Delete all uploads
        db.query(Upload).delete()

        # Commit the changes
        db.commit()

        print("Database cleanup completed. All previous data has been removed.")
    except Exception as e:
        db.rollback()
        print(f"Error during database cleanup: {e}")
    finally:
        db.close()
