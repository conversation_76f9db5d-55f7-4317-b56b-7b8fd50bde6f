from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID
from pydantic import BaseModel, Field

class UploadResponse(BaseModel):
    """Response model for file upload."""
    upload_id: UUID
    filename: str
    status: str
    message: str = "File uploaded successfully. Processing started."

class StatusResponse(BaseModel):
    """Response model for upload status."""
    upload_id: UUID
    status: str
    filename: str
    upload_time: datetime
    error_message: Optional[str] = None
    output_files: Optional[List[Dict[str, Any]]] = None

class OutputFileResponse(BaseModel):
    """Response model for output file information."""
    id: UUID
    filename: str
    file_type: str
    created_at: datetime
    download_url: str
