from datetime import datetime, timezone
import logging
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.database import get_db, User
from app.models.auth import UserCreate, UserVerify, UserResponse, Token, UserLogin, SetPasswordRequest
from app.utils.email import generate_verification_code, send_verification_email
from app.utils.auth import create_access_token, is_verification_code_expired, get_current_user, get_password_hash, verify_password

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='auth.log',
    filemode='a'
)
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/auth",
    tags=["authentication"],
    responses={404: {"description": "Not found"}},
)

@router.post("/signup", status_code=status.HTTP_201_CREATED)
async def signup(user_data: UserCreate, db: Session = Depends(get_db)):
    """
    Register a new user with email.

    A verification code will be sent to the provided email.
    This is now a wrapper around the request-verification endpoint.
    """
    # Forward to the request-verification endpoint
    return await request_verification(user_data, db)

@router.post("/verify", response_model=Token)
async def verify(verification_data: UserVerify, db: Session = Depends(get_db)):
    """
    Verify a user's email with the provided code.

    Returns an access token if verification is successful.
    """
    logger.info(f"Verification request received for email: {verification_data.email}, code: {verification_data.code}")
    user = db.query(User).filter(User.email == verification_data.email).first()

    if not user:
        logger.error(f"User not found: {verification_data.email}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )

    logger.info(f"User found: {user.email}, is_active: {user.is_active}, verification_code: {user.verification_code}")

    if user.is_active:
        logger.error(f"User already verified: {user.email}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User already verified",
        )

    if user.verification_code != verification_data.code:
        logger.error(f"Invalid verification code: expected {user.verification_code}, got {verification_data.code}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid verification code",
        )

    if is_verification_code_expired(user.code_created_at):
        logger.error(f"Verification code expired: created at {user.code_created_at}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Verification code expired",
        )

    # Activate user
    user.is_active = True
    user.verification_code = None
    user.code_created_at = None
    user.last_login = datetime.now(timezone.utc)
    db.commit()

    # Create access token
    access_token = create_access_token(
        data={"sub": user.email, "user_id": str(user.id)},
    )

    return {"access_token": access_token, "token_type": "bearer"}

@router.post("/set-password", response_model=Token)
async def set_password(password_data: SetPasswordRequest, db: Session = Depends(get_db)):
    """
    Set a password for a verified user.

    This endpoint should be called after email verification.
    """
    logger.info(f"Set password request received for email: {password_data.email}")

    # Find the user
    user = db.query(User).filter(User.email == password_data.email).first()

    if not user:
        logger.error(f"User not found: {password_data.email}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )

    if not user.is_active:
        logger.error(f"User not verified: {password_data.email}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User not verified. Please verify your email first.",
        )

    # Hash the password and save it
    hashed_password = get_password_hash(password_data.password)
    user.hashed_password = hashed_password
    user.last_login = datetime.now(timezone.utc)
    db.commit()

    # Create access token
    access_token = create_access_token(
        data={"sub": user.email, "user_id": str(user.id)},
    )

    logger.info(f"Password set successfully for user: {user.email}")
    return {"access_token": access_token, "token_type": "bearer"}

@router.post("/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    """
    Login with username (email) and password.

    This endpoint is compatible with Swagger UI's OAuth2 authentication.
    The username field is used for the email address.
    """
    logger.info(f"Login request received for email: {form_data.username}")

    # Use the username field as email
    user = db.query(User).filter(User.email == form_data.username).first()

    if not user:
        logger.error(f"User not found: {form_data.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not user.is_active:
        logger.error(f"User not verified: {form_data.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not verified. Please verify your email first.",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not user.hashed_password:
        logger.error(f"User has no password: {form_data.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Password not set. Please set a password first.",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Verify password
    if not verify_password(form_data.password, user.hashed_password):
        logger.error(f"Invalid password for user: {form_data.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Update last login
    user.last_login = datetime.now(timezone.utc)
    db.commit()

    # Create access token
    access_token = create_access_token(
        data={"sub": user.email, "user_id": str(user.id)},
    )

    logger.info(f"Login successful for user: {user.email}")
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Get information about the current user."""
    return current_user

@router.post("/request-verification")
async def request_verification(user_data: UserCreate, db: Session = Depends(get_db)):
    """
    Request a new verification code.

    This can be used for initial signup, password reset, or if the verification code expired.
    """
    logger.info(f"Verification code request received for email: {user_data.email}")

    # Check if user already exists
    user = db.query(User).filter(User.email == user_data.email).first()

    if not user:
        # Create new user
        verification_code = generate_verification_code()

        new_user = User(
            email=user_data.email,
            verification_code=verification_code,
            code_created_at=datetime.now(timezone.utc),
        )

        try:
            db.add(new_user)
            db.commit()
            db.refresh(new_user)
        except IntegrityError:
            db.rollback()
            logger.error(f"Error creating user: {user_data.email}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered",
            )

        # Send verification email
        success = send_verification_email(new_user.email, verification_code)

        if success:
            logger.info(f"Verification code sent to new user: {new_user.email}")
            return {"message": "Verification code sent to your email"}
        else:
            logger.error(f"Failed to send verification email to {new_user.email}")
            return {"message": "User registered but failed to send verification email. Please check your email configuration."}

    # User exists, generate a new code
    verification_code = generate_verification_code()
    user.verification_code = verification_code
    user.code_created_at = datetime.now(timezone.utc)
    db.commit()

    # Send verification email
    success = send_verification_email(user.email, verification_code)

    if success:
        logger.info(f"Verification code sent to existing user: {user.email}")
        return {"message": "Verification code sent to your email"}
    else:
        logger.error(f"Failed to send verification email to {user.email}")
        return {"message": "Failed to send verification email. Please check your email configuration."}
